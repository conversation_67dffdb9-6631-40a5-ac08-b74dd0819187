import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { supabase, RoomMessage, createAuthenticatedSupabaseClient } from '@/lib/supabase';
import { toast } from 'sonner';
import { RealtimeChannel } from '@supabase/supabase-js';
import { useSmartRefresh } from './useSmartRefresh';

interface TypingUser {
  id: string;
  name: string;
  timestamp: number;
}

interface OnlineUser {
  id: string;
  name: string;
  image?: string;
  joinedAt: string;
}

export const useRoomMessages = (roomId: string) => {
  const { user } = useUser();
  const { getToken, isSignedIn } = useAuth();
  const [messages, setMessages] = useState<RoomMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const channelRef = useRef<RealtimeChannel | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get authenticated Supabase client
  const getAuthenticatedClient = useCallback(async () => {
    if (!isSignedIn) {
      return supabase;
    }

    try {
      const token = await getToken({ template: 'supabase' });
      if (token) {
        return createAuthenticatedSupabaseClient(token);
      }
      return supabase;
    } catch (error) {
      console.error('Error getting authenticated client:', error);
      return supabase;
    }
  }, [getToken, isSignedIn]);

  // Fetch existing messages
  const fetchMessages = useCallback(async () => {
    if (!roomId) return;

    try {
      const client = await getAuthenticatedClient();
      const { data, error } = await client
        .from('room_messages')
        .select('*')
        .eq('room_id', roomId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      setMessages(data || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error('Failed to load messages');
    } finally {
      setLoading(false);
    }
  }, [roomId, getAuthenticatedClient]);

  // Send typing indicator
  const sendTypingIndicator = useCallback(async (isTyping: boolean) => {
    if (!user || !roomId || !channelRef.current) return;

    try {
      await channelRef.current.send({
        type: 'broadcast',
        event: 'typing',
        payload: {
          user_id: user.id,
          user_name: user.fullName || user.firstName || 'Anonymous',
          is_typing: isTyping,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('Error sending typing indicator:', error);
    }
  }, [user, roomId]);

  // Send a new message
  const sendMessage = useCallback(async (content: string) => {
    if (!user || !roomId || !content.trim()) return;

    try {
      // Stop typing indicator
      await sendTypingIndicator(false);

      const client = await getAuthenticatedClient();
      const messageData = {
        room_id: roomId,
        user_id: user.id,
        user_name: user.fullName || user.firstName || 'Anonymous',
        user_image: user.imageUrl,
        content: content.trim(),
        message_type: 'message' as const
      };

      // Optimistic update - add message immediately for better UX
      const optimisticMessage = {
        ...messageData,
        id: `temp-${Date.now()}`, // Temporary ID
        created_at: new Date().toISOString()
      };

      setMessages(prev => [...prev, optimisticMessage]);

      const { data, error } = await client
        .from('room_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      // Replace optimistic message with real one
      if (data) {
        setMessages(prev => prev.map(msg =>
          msg.id === optimisticMessage.id ? data : msg
        ));
      }

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');

      // Remove optimistic message on error
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('temp-')));
    }
  }, [user, roomId, getAuthenticatedClient, sendTypingIndicator]);

  // Clean up typing users periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setTypingUsers(prev => prev.filter(user => now - user.timestamp < 3000)); // Remove after 3 seconds
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Smart background refresh for messages using utility hook
  useSmartRefresh({
    refreshFunction: fetchMessages,
    enabled: !!roomId,
    connectionStatus: 'disconnected', // Default to disconnected for fallback polling
    idleThreshold: 15000, // 15 seconds for messages
    connectedInterval: 360000, // 6 minutes when real-time works (messages change frequently)
    disconnectedInterval: 180000, // 3 minutes when real-time fails
    debug: false
  });

  // Set up real-time subscription with enhanced features
  useEffect(() => {
    if (!roomId || !user) return;

    let channel: RealtimeChannel | null = null;

    const setupSubscription = async () => {
      try {
        console.log('Setting up realtime subscription for room:', roomId);

        // Initial fetch
        await fetchMessages();

        // Set up real-time subscription with base client (not authenticated)
        console.log('Setting up realtime subscription with base client');

        channel = supabase
          .channel(`room:${roomId}`)
          // Listen to database changes
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'room_messages',
              filter: `room_id=eq.${roomId}`
            },
            (payload) => {
              console.log('New message received via realtime:', payload);
              const newMessage = payload.new as RoomMessage;
              setMessages(prev => {
                // Avoid duplicates (including optimistic updates)
                if (prev.some(msg => msg.id === newMessage.id || (msg.content === newMessage.content && msg.user_id === newMessage.user_id))) {
                  // Replace optimistic message with real one
                  return prev.map(msg =>
                    (msg.id.startsWith('temp-') && msg.content === newMessage.content && msg.user_id === newMessage.user_id)
                      ? newMessage
                      : msg
                  ).filter(msg => msg.id === newMessage.id || !msg.id.startsWith('temp-'));
                }
                return [...prev, newMessage];
              });
            }
          )
          // Listen to typing indicators
          .on('broadcast', { event: 'typing' }, (payload) => {
            const { user_id, user_name, is_typing, timestamp } = payload.payload;

            if (user_id === user.id) return; // Ignore own typing

            setTypingUsers(prev => {
              const filtered = prev.filter(u => u.id !== user_id);
              if (is_typing) {
                return [...filtered, { id: user_id, name: user_name, timestamp }];
              }
              return filtered;
            });
          })
          // Listen to presence changes
          .on('presence', { event: 'sync' }, () => {
            const state = channel?.presenceState();
            if (state) {
              const users: OnlineUser[] = [];
              Object.keys(state).forEach(userId => {
                const presences = state[userId];
                if (presences.length > 0) {
                  const presence = presences[0];
                  users.push({
                    id: userId,
                    name: presence.name || 'Anonymous',
                    image: presence.image,
                    joinedAt: presence.joined_at || new Date().toISOString()
                  });
                }
              });
              setOnlineUsers(users);
            }
          })
          .on('presence', { event: 'join' }, ({ key, newPresences }) => {
            console.log('User joined:', key, newPresences);
          })
          .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
            console.log('User left:', key, leftPresences);
          });

        // Subscribe to the channel first
        const status = await channel.subscribe((status, err) => {
          console.log('Channel subscription status:', status);
          if (err) {
            console.error('Channel subscription error:', err);
            // Don't show error toast immediately, let the fallback handle it
            console.warn('Real-time chat connection failed, using fallback polling');
            return;
          }

          if (status === 'SUBSCRIBED') {
            console.log('Successfully subscribed to room channel');
            // Track user presence only after successful subscription
            channel.track({
              name: user.fullName || user.firstName || 'Anonymous',
              image: user.imageUrl,
              joined_at: new Date().toISOString()
            }).catch(error => {
              console.error('Error tracking presence:', error);
            });
          } else if (status === 'CHANNEL_ERROR') {
            console.error('Channel error occurred');
            console.warn('Real-time chat connection error, using fallback polling');
          } else if (status === 'TIMED_OUT') {
            console.error('Channel subscription timed out');
            console.warn('Real-time chat connection timed out, using fallback polling');
          } else if (status === 'CLOSED') {
            console.log('Channel subscription closed');
          }
        });

        channelRef.current = channel;

      } catch (error) {
        console.error('Error setting up subscription:', error);
        toast.error('Failed to connect to real-time chat');
      }
    };

    setupSubscription();

    return () => {
      if (channel) {
        console.log('Cleaning up channel subscription');
        channel.untrack();
        supabase.removeChannel(channel);
      }
      channelRef.current = null;
    };
  }, [roomId, user, fetchMessages, getAuthenticatedClient]);

  // Handle typing with debounce
  const handleTyping = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Send typing indicator
    sendTypingIndicator(true);

    // Stop typing after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      sendTypingIndicator(false);
    }, 2000);
  }, [sendTypingIndicator]);

  // Stop typing
  const stopTyping = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    sendTypingIndicator(false);
  }, [sendTypingIndicator]);

  return {
    messages,
    loading,
    sendMessage,
    typingUsers,
    onlineUsers,
    handleTyping,
    stopTyping
  };
};
